<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.newtransaction.NewTransactionFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- New Transaction Form Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_new_transaction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp" >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_new_transaction_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/new_transaction"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <!-- Date Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_date"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_date"
                        app:helperText="*"
                        app:helperTextTextColor="?attr/colorError">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:clickable="true" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Title Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_title"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Amount and Mode Row -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- Amount Field -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_transaction_amount"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/transaction_amount"
                            app:helperText="*"
                            app:helperTextTextColor="?attr/colorError">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_transaction_amount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number"
                                android:maxLines="1" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Mode Dropdown -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_transaction_mode"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/transaction_mode"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            app:helperText="*"
                            app:helperTextTextColor="?attr/colorError">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_transaction_mode"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- Bank and Category Row -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- Bank Dropdown -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_transaction_bank"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/transaction_bank"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_transaction_bank"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Category Dropdown -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_transaction_category"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/transaction_category"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            app:helperText="*"
                            app:helperTextTextColor="?attr/colorError">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_transaction_category"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- Tags Multi-Select -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_transaction_tags"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/transaction_tags"
                        app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_transaction_tags"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:clickable="true"
                            android:textSize="12sp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Submit Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_submit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="@string/submit"
                        android:textSize="16sp"
                        app:cornerRadius="8dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error message"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
