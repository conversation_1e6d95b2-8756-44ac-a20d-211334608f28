package ir.rahavardit.ariel.utils

/**
 * Utility class for Jalali (Persian) calendar operations.
 */
object JalaliDateUtils {

    private val persianMonthNames = arrayOf(
        "فروردین", "اردیبهشت", "خرد<PERSON>", "تیر", "مرد<PERSON>", "شهریور",
        "مهر", "آبان", "آذر", "دی", "بهمن", "اسفند"
    )

    /**
     * Converts Gregorian date to Jalali date.
     * 
     * @param gy Gregorian year
     * @param gm Gregorian month (1-12)
     * @param gd Gregorian day
     * @return IntArray containing [jalali_year, jalali_month, jalali_day]
     */
    fun gregorianToJalali(gy: Int, gm: Int, gd: Int): IntArray {
        val g_d_m = intArrayOf(0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334)
        
        val jy = if (gy <= 1600) 0 else 979
        var gy2 = if (gy <= 1600) gy - 621 else gy - 1600
        val gm2 = if (gm > 2) gm + 1 else gm
        
        val days = (365 * gy2) + ((gy2 + 3) / 4) + ((gy2 + 99) / 100) - 
                   ((gy2 + 399) / 400) - 80 + gd + g_d_m[gm - 1]
        
        var jy2 = -14 + 33 * (days / 12053)
        var days2 = days - (12053 * jy2 + 14)

        if (days2 < 0) {
            jy2 -= 1
            days2 = days - (12053 * jy2 + 14)
        }
        
        val jp = 20 + 33 * (days2 / 12053)
        days2 %= 12053
        
        val jy3 = jy2 + (jp / 33)
        val jp2 = jp % 33
        val jy4 = jy3 + (jp2 / 4)
        val jp3 = jp2 % 4
        
        val jy5 = jy4 + jy
        
        if (days2 < 186) {
            val jm = 1 + days2 / 31
            val jd = 1 + (days2 % 31)
            return intArrayOf(jy5, jm, jd)
        } else {
            val jm = 7 + (days2 - 186) / 30
            val jd = 1 + ((days2 - 186) % 30)
            return intArrayOf(jy5, jm, jd)
        }
    }

    /**
     * Converts Jalali date to Gregorian date.
     * 
     * @param jy Jalali year
     * @param jm Jalali month (1-12)
     * @param jd Jalali day
     * @return IntArray containing [gregorian_year, gregorian_month, gregorian_day]
     */
    fun jalaliToGregorian(jy: Int, jm: Int, jd: Int): IntArray {
        val jy2 = if (jy >= 0) jy + 1595 else jy + 1596
        val days = (365 * jy2) + ((jy2 / 33) * 8) + (((jy2 % 33) + 3) / 4) + 78 + jd +
                   if (jm < 7) (jm - 1) * 31 else (jm - 7) * 30 + 186
        
        val gy = 400 * (days / 146097)
        var days2 = days % 146097
        
        val leap = if (days2 >= 36525) {
            days2 -= 1
            val gy2 = 100 * (days2 / 36524)
            days2 %= 36524
            if (days2 >= 365) days2 += 1
            gy2
        } else 0
        
        val gy3 = 4 * (days2 / 1461)
        days2 %= 1461
        
        val gy4 = if (days2 >= 366) {
            days2 -= 1
            days2 / 365
        } else 0
        
        val gy5 = gy + leap + gy3 + gy4 + 1
        
        val sal_a = intArrayOf(0, 31, if ((gy5 % 4 == 0 && gy5 % 100 != 0) || (gy5 % 400 == 0)) 29 else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
        
        var gm = 0
        while (gm < 13 && days2 >= sal_a[gm]) {
            days2 -= sal_a[gm]
            gm++
        }
        
        if (gm > 12) {
            gm = 12
            days2 = sal_a[12] - 1
        }
        
        val gd = days2 + 1
        
        return intArrayOf(gy5, gm, gd)
    }

    /**
     * Gets the Persian name of a month.
     * 
     * @param month Month number (1-12)
     * @return Persian month name
     */
    fun getPersianMonthName(month: Int): String {
        return if (month in 1..12) persianMonthNames[month - 1] else ""
    }

    /**
     * Gets the number of days in a Jalali month.
     * 
     * @param year Jalali year
     * @param month Jalali month (1-12)
     * @return Number of days in the month
     */
    fun getDaysInJalaliMonth(year: Int, month: Int): Int {
        return when (month) {
            in 1..6 -> 31
            in 7..11 -> 30
            12 -> if (isJalaliLeapYear(year)) 30 else 29
            else -> 0
        }
    }

    /**
     * Checks if a Jalali year is a leap year.
     * 
     * @param year Jalali year
     * @return True if leap year, false otherwise
     */
    private fun isJalaliLeapYear(year: Int): Boolean {
        val breaks = intArrayOf(-14, 3, 13, 84, 111, 138, 166, 230, 266, 498, 746, 1029, 1058, 1082, 1115, 1153, 1370)
        
        val gy = year + 1029
        var leap = -14
        var jp = breaks[0]
        
        var jump = 0
        for (j in 1 until breaks.size) {
            val jm = breaks[j]
            jump = jm - jp
            if (year < jm) break
            leap += (jump / 33) * 8 + ((jump % 33) / 4)
            jp = jm
        }
        
        val n = year - jp
        if (n < jump) {
            leap += ((n - 1) / 33) * 8 + (((n - 1) % 33 + 3) / 4)
            if ((jump % 33) == 4 && (jump - n) == 4) leap++
        }
        
        return (leap + 4) % 1029 % 33 % 4 == 1
    }

    /**
     * Formats a Jalali date as a string.
     * 
     * @param year Jalali year
     * @param month Jalali month
     * @param day Jalali day
     * @return Formatted date string (YYYY/MM/DD)
     */
    fun formatJalaliDate(year: Int, month: Int, day: Int): String {
        return String.format("%04d/%02d/%02d", year, month, day)
    }
}
