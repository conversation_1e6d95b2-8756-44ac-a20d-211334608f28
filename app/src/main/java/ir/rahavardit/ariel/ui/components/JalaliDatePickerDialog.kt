package ir.rahavardit.ariel.ui.components

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.NumberPicker
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.google.android.material.button.MaterialButton
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils
import java.util.Calendar

/**
 * Custom Jalali date picker dialog.
 */
class JalaliDatePickerDialog(
    context: Context,
    private val onDateSelected: (year: Int, month: Int, day: Int) -> Unit,
    initialYear: Int? = null,
    initialMonth: Int? = null,
    initialDay: Int? = null
) : Dialog(context) {

    private val currentJalali: IntArray
    private val initialYear: Int
    private val initialMonth: Int
    private val initialDay: Int

    init {
        // Get current Jalali date
        val calendar = Calendar.getInstance()
        currentJalali = JalaliDateUtils.gregorianToJalali(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH)
        )

        this.initialYear = initialYear ?: currentJalali[0]
        this.initialMonth = initialMonth ?: currentJalali[1]
        this.initialDay = initialDay ?: currentJalali[2]
    }

    private lateinit var binding: DialogJalaliDatePickerBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = DialogJalaliDatePickerBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)

        setupDatePickers()
        setupButtons()
        
        // Set dialog properties
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupDatePickers() {
        // Setup year picker
        val currentYear = currentJalali[0]
        val yearRange = (currentYear - 10)..(currentYear + 10)

        binding.yearPicker.apply {
            minValue = 0
            maxValue = yearRange.count() - 1
            displayedValues = yearRange.map { PersianUtils.convertToPersianNumerals(it) }.toTypedArray()
            value = yearRange.indexOf(initialYear)
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }

        // Setup month picker
        val monthNames = (1..12).map { JalaliDateUtils.getPersianMonthName(it) }.toTypedArray()
        
        binding.monthPicker.apply {
            minValue = 0
            maxValue = 11
            displayedValues = monthNames
            value = initialMonth - 1
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }

        // Setup day picker
        updateDayPicker()

        // Add listeners to update day picker when year or month changes
        binding.yearPicker.setOnValueChangedListener { _, _, _ -> updateDayPicker() }
        binding.monthPicker.setOnValueChangedListener { _, _, _ -> updateDayPicker() }
    }

    private fun updateDayPicker() {
        val selectedYear = getSelectedYear()
        val selectedMonth = getSelectedMonth()
        val daysInMonth = JalaliDateUtils.getDaysInJalaliMonth(selectedYear, selectedMonth)

        val currentDay = binding.dayPicker.value + 1

        binding.dayPicker.apply {
            minValue = 0
            maxValue = daysInMonth - 1
            displayedValues = (1..daysInMonth).map { PersianUtils.convertToPersianNumerals(it) }.toTypedArray()

            // Preserve current day if possible, otherwise set to last day of month
            value = if (currentDay <= daysInMonth) currentDay - 1 else daysInMonth - 1
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }
    }



    private fun setupButtons() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }

        binding.btnOk.setOnClickListener {
            val year = getSelectedYear()
            val month = getSelectedMonth()
            val day = getSelectedDay()
            
            onDateSelected(year, month, day)
            dismiss()
        }
    }

    private fun getSelectedYear(): Int {
        val currentYear = currentJalali[0]
        val yearRange = (currentYear - 10)..(currentYear + 10)
        return yearRange.elementAt(binding.yearPicker.value)
    }

    private fun getSelectedMonth(): Int {
        return binding.monthPicker.value + 1
    }

    private fun getSelectedDay(): Int {
        return binding.dayPicker.value + 1
    }
}
